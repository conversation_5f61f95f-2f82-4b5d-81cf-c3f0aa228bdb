// src/components/Salary.js
import React, { useState, useEffect, useCallback } from "react";
import {
  Container,
  Typo<PERSON>,
  <PERSON>ton,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  Input,
  Checkbox,
  Box,
  TextField,
  CircularProgress,
  // FormControl,
  // InputLabel,
  // Select,
  // MenuItem,
  Chip,
  TablePagination,
  Autocomplete,
  Alert,
} from "@mui/material";
import * as XLSX from "xlsx";
import { Link } from "react-router-dom";
import {
  loadSalaryData,
  saveData,
  STORE_NAMES,
  loadPaginationState,
  savePaginationState,
} from "../db";

const Salary = () => {
  const [salaryData, setSalaryData] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedEntries, setSelectedEntries] = useState(new Set());
  // const [selectAll, setSelectAll] = useState(false); // Removed unused variable
  const [loading, setLoading] = useState(false);
  const [importSummary, setImportSummary] = useState("");
  const [errorMessages, setErrorMessages] = useState([]);
  const [uniqueMonths, setUniqueMonths] = useState(["All Months"]);
  const [selectedMonths, setSelectedMonths] = useState(["All Months"]);
  const [filteredData, setFilteredData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(1);
  // New state to track unsaved changes
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  // Load salary data from IndexedDB
  const fetchSalaryData = useCallback(async () => {
    setLoading(true);
    try {
      const fetchedData = await loadSalaryData();
      setSalaryData(fetchedData);
      setUnsavedChanges(false); // Data loaded from DB is considered saved

      // Update unique months list
      const months = fetchedData.map((item) => item.payrollMonth);
      const unique = ["All Months", ...Array.from(new Set(months)).sort()];
      setUniqueMonths(unique);

      // Apply month filter
      const monthFiltered =
        selectedMonths.includes("All Months")
          ? fetchedData
          : fetchedData.filter(
              (item) => selectedMonths.includes(item.payrollMonth)
            );
      // Also apply search filter below
      if (searchQuery.trim() !== "") {
        setFilteredData(
          monthFiltered.filter((item) =>
            item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.payrollMonth.toLowerCase().includes(searchQuery.toLowerCase())
          )
        );
      } else {
        setFilteredData(monthFiltered);
      }
    } catch (error) {
      console.error("Error loading salary data:", error);
      setErrorMessages([error.message]);
    }
    setLoading(false);
  }, [selectedMonths, searchQuery]);

  // Initial data load and subscribe to DB changes
  useEffect(() => {
    fetchSalaryData();
    const handleDBChange = () => fetchSalaryData();
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [fetchSalaryData]);

  // Save and load pagination state
  useEffect(() => {
    loadPaginationState("salary").then((state) => {
      setCurrentPage(state.currentPage || 1);
      setItemsPerPage(state.itemsPerPage || 100);
    });
  }, []);

  useEffect(() => {
    savePaginationState("salary", { currentPage, itemsPerPage });
  }, [currentPage, itemsPerPage]);

  useEffect(() => {
    const pages = Math.ceil(filteredData.length / itemsPerPage);
    setTotalPages(pages);
    if (currentPage > pages) setCurrentPage(1);
  }, [filteredData, itemsPerPage, currentPage]);

  // Reapply filters when salaryData, selectedMonths or searchQuery changes
  useEffect(() => {
    if (salaryData.length > 0) {
      const monthFiltered = selectedMonths.includes("All Months")
        ? salaryData
        : salaryData.filter((item) =>
            selectedMonths.includes(item.payrollMonth)
          );
      const searchFiltered =
        searchQuery.trim() !== ""
          ? monthFiltered.filter(
              (item) =>
                item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.payrollMonth.toLowerCase().includes(searchQuery.toLowerCase())
            )
          : monthFiltered;
      setFilteredData(searchFiltered);
    } else {
      setFilteredData([]);
    }
  }, [salaryData, selectedMonths, searchQuery]);

  const getCurrentPageData = () => {
    const start = (currentPage - 1) * itemsPerPage;
    return filteredData.slice(start, start + itemsPerPage);
  };

  // const exportData = useMemo(() => {
  //   if (salaryData.length === 0) return [];
  //   return selectedMonths.includes("All Months")
  //     ? salaryData
  //     : salaryData.filter((record) =>
  //         selectedMonths.includes(record.payrollMonth)
  //       );
  // }, [salaryData, selectedMonths]); // Removed unused variable

  const handleExport = useCallback(() => {
    const selectedData = filteredData.filter((item) =>
      selectedEntries.has(item.id)
    );
    if (selectedData.length === 0) {
      setErrorMessages(["Please select at least one record to export"]);
      return;
    }
    const ws = XLSX.utils.json_to_sheet(selectedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Salary Data");
    XLSX.writeFile(wb, "salary_data.xlsx");
    setErrorMessages([]);
  }, [filteredData, selectedEntries]);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;
    event.target.value = null;
    setLoading(true);
    setImportSummary("");
    setErrorMessages([]);
    const reader = new FileReader();
    reader.readAsBinaryString(file);
    reader.onload = (e) => {
      try {
        const binaryStr = e.target.result;
        const workbook = XLSX.read(binaryStr, { type: "binary" });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const parsedData = XLSX.utils.sheet_to_json(sheet, {
          header: 1,
          defval: "",
        });
        let errors = [];
        let importedCount = 0,
          updatedCount = 0;
        const existingRecords = new Map(
          salaryData.map((item) => [`${item.name}-${item.payrollMonth}`, item])
        );
        const headerRow = parsedData[0].map((h) => h.toString().trim());
        if (
          JSON.stringify(headerRow) !==
          JSON.stringify(["Payroll Month", "Name", "GROSS", "PF", "SALARY MASTER"])
        ) {
          errors.push(
            "Warning: Excel file header does not match expected template. Expected: Payroll Month, Name, GROSS, PF, SALARY MASTER."
          );
        }
        const newEntries = parsedData
          .slice(1)
          .map((row, index) => {
            if (row.every((cell) => cell === "")) return null;
            const payrollMonth = row[0] ? row[0].toString().trim() : "";
            const name = row[1] ? row[1].toString().trim() : "";
            const gross = row[2] ? parseFloat(row[2]) : 0;
            const pf = row[3] ? parseFloat(row[3]) : 0;
            const salaryMaster = row[4] ? row[4].toString().trim() : "";
            if (!payrollMonth || !name || gross === 0) {
              errors.push(`Error in row ${index + 2}: Missing mandatory fields.`);
              return null;
            }
            const recordKey = `${name}-${payrollMonth}`;
            const existingRecord = existingRecords.get(recordKey);
            if (existingRecord) {
              updatedCount++;
              return {
                ...existingRecord,
                gross,
                pf,
                salaryMaster,
                updatedAt: new Date().toISOString(),
                source: "import-update",
              };
            } else {
              importedCount++;
              return {
                id: Date.now() + index,
                payrollMonth,
                name,
                gross,
                pf,
                salaryMaster,
                createdAt: new Date().toISOString(),
                source: "import-new",
              };
            }
          })
          .filter((row) => row !== null);
        // Remove records that will be updated
        const updatedKeys = new Set(
          newEntries.map((entry) => `${entry.name}-${entry.payrollMonth}`)
        );
        const filteredExisting = salaryData.filter(
          (item) => !updatedKeys.has(`${item.name}-${item.payrollMonth}`)
        );
        setSalaryData([...filteredExisting, ...newEntries]);
        setUnsavedChanges(true); // Data has been modified, flag unsaved changes
        const summaryMessages = [];
        if (importedCount > 0)
          summaryMessages.push(`${importedCount} new records imported.`);
        if (updatedCount > 0)
          summaryMessages.push(`${updatedCount} records updated.`);
        if (errors.length > 0)
          summaryMessages.push(`${errors.length} errors encountered.`);
        setImportSummary(summaryMessages.join(" "));
        if (errors.length > 0) setErrorMessages(errors);
      } catch (error) {
        console.error("Error processing Excel file:", error);
        setErrorMessages(["Error processing file:", error.message]);
      }
      setLoading(false);
    };
    reader.onerror = () => {
      setErrorMessages(["Error reading file."]);
      setLoading(false);
    };
  };

  const handleSelectOne = (event, id) => {
    const newSelected = new Set(selectedEntries);
    if (event.target.checked) newSelected.add(id);
    else newSelected.delete(id);
    setSelectedEntries(newSelected);
    // setSelectAll(newSelected.size === filteredData.length); // Removed unused variable
  };

  const handleSelectAll = (event) => {
    const newSelectAll = event.target.checked;
    // setSelectAll(newSelectAll); // Removed unused variable
    setSelectedEntries(
      newSelectAll ? new Set(filteredData.map((item) => item.id)) : new Set()
    );
  };

  const handleDeleteSelected = () => {
    if (window.confirm("Are you sure you want to delete selected records?")) {
      const newEntries = salaryData.filter(
        (row) => !selectedEntries.has(row.id)
      );
      setSalaryData(newEntries);
      setSelectedEntries(new Set());
      // setSelectAll(false); // Removed unused variable
      setUnsavedChanges(true); // Data modified, unsaved changes exist
    }
  };

  const handleSave = () => {
    saveData(STORE_NAMES.SALARY, salaryData)
      .then(() => {
        alert("Salary data saved successfully!");
        setUnsavedChanges(false); // Data is now saved
      })
      .catch((err) => alert("Error saving data."));
  };

  const MonthDropdown = () => (
    <Autocomplete
      multiple
      value={selectedMonths}
      onChange={(event, newValue) => setSelectedMonths(newValue)}
      options={uniqueMonths}
      getOptionLabel={(option) => option}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Select Months"
          placeholder="Select months..."
          fullWidth
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                {params.InputProps.startAdornment}
                <Button
                  size="small"
                  onClick={() => {
                    if (selectedMonths.length === uniqueMonths.length) {
                      setSelectedMonths(["All Months"]);
                    } else {
                      setSelectedMonths(uniqueMonths);
                    }
                  }}
                  sx={{ background: "transparent", color: "#1976d2" }}
                >
                  {selectedMonths.length === uniqueMonths.length
                    ? "Deselect All"
                    : "Select All"}
                </Button>
              </Box>
            ),
          }}
          sx={{ color: "#1976d2", borderColor: "#1976d2" }}
        />
      )}
      renderOption={(props, option, { selected }) => (
        <li {...props}>
          <Checkbox style={{ marginRight: 8 }} checked={selected} />
          {option}
        </li>
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            variant="outlined"
            label={option}
            {...getTagProps({ index })}
            size="small"
            sx={{ borderColor: "#1976d2", color: "#1976d2" }}
          />
        ))
      }
      disableCloseOnSelect
    />
  );

  return (
    <Container sx={{ padding: 4 }}>
      <Box sx={{ mb: 2 }}>
        <Button variant="outlined" component={Link} to="/">
          Back to Dashboard
        </Button>
      </Box>
      {/* Show a pop-up alert if unsaved changes exist */}
      {unsavedChanges && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Data has changed. Please click the Save Data button to persist your changes.
        </Alert>
      )}
      <Typography variant="h4" gutterBottom>Salary Data</Typography>
      <Box
        sx={{ mb: 2, display: "flex", gap: 2, alignItems: "center" }}
      >
        <Button variant="contained" component="label">
          Import Excel
          <Input
            type="file"
            accept=".xlsx, .xls"
            onChange={handleFileUpload}
            sx={{ display: "none" }}
          />
        </Button>
        <Button variant="contained" onClick={handleExport} sx={{ mr: 2 }}>
          Export Selected
        </Button>
        <Button variant="contained" onClick={handleSave} sx={{ mr: 2 }}>
          Save Data
        </Button>
        <Button
          variant="contained"
          color="error"
          onClick={handleDeleteSelected}
        >
          Delete Selected
        </Button>
      </Box>
      <TextField
        label="Search"
        variant="outlined"
        fullWidth
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        sx={{ mb: 2 }}
      />
      <Box sx={{ mb: 2 }}>
        <MonthDropdown />
      </Box>
      {loading && (
        <Box display="flex" alignItems="center" mt={2}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Loading...
          </Typography>
        </Box>
      )}
      {importSummary && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: "#fff3cd" }}>
          <Typography variant="h6">Import Summary</Typography>
          <Typography>{importSummary}</Typography>
          {errorMessages.map((err, idx) => (
            <Typography key={idx} variant="body2">
              {err}
            </Typography>
          ))}
        </Box>
      )}
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table>
          <TableHead sx={{ backgroundColor: "#1976d2" }}>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={selectedEntries.size === filteredData.length}
                  indeterminate={
                    selectedEntries.size > 0 &&
                    selectedEntries.size < filteredData.length
                  }
                  onChange={handleSelectAll}
                />
              </TableCell>
              {[
                "Sl No",
                "Payroll Month",
                "Name",
                "GROSS",
                "PF",
                "SALARY MASTER",
              ].map((header, idx) => (
                <TableCell
                  key={idx}
                  sx={{ fontWeight: "bold", color: "black" }}
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {getCurrentPageData().length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No data available. Import an Excel file.
                </TableCell>
              </TableRow>
            ) : (
              getCurrentPageData().map((row) => (
                <TableRow
                  key={row.id}
                  sx={{
                    "&:nth-of-type(even)": { backgroundColor: "#f4f6f8" },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedEntries.has(row.id)}
                      onChange={(event) => handleSelectOne(event, row.id)}
                    />
                  </TableCell>
                  <TableCell>{row.slNo}</TableCell>
                  <TableCell>{row.payrollMonth}</TableCell>
                  <TableCell>{row.name}</TableCell>
                  <TableCell>{row.gross}</TableCell>
                  <TableCell>{row.pf}</TableCell>
                  <TableCell>{row.salaryMaster}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {totalPages > 1 && (
        <TablePagination
          component="div"
          count={filteredData.length}
          page={currentPage - 1}
          onPageChange={(event, newPage) => setCurrentPage(newPage + 1)}
          rowsPerPage={itemsPerPage}
          onRowsPerPageChange={(event) =>
            setItemsPerPage(parseInt(event.target.value, 10))
          }
        />
      )}
    </Container>
  );
};

export default Salary;
