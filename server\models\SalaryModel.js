const BaseModel = require('./BaseModel');

class SalaryModel extends BaseModel {
  constructor() {
    super('salary');
  }

  getSearchableFields() {
    return ['name', 'payrollMonth', 'salaryMaster'];
  }

  getDefaultOrderBy() {
    return 'createdAt DESC';
  }

  // Find by employee and payroll month
  findByEmployeeAndMonth(name, payrollMonth) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE name = ? AND payrollMonth = ?`;
      this.db.get(query, [name, payrollMonth], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  // Get unique values for filters
  getUniqueMonths() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT payrollMonth FROM ${this.tableName} WHERE payrollMonth IS NOT NULL AND payrollMonth != '' ORDER BY payrollMonth`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.payrollMonth));
        }
      });
    });
  }

  getUniqueEmployees() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT name FROM ${this.tableName} WHERE name IS NOT NULL AND name != '' ORDER BY name`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.name));
        }
      });
    });
  }

  getUniqueSalaryMasters() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT salaryMaster FROM ${this.tableName} WHERE salaryMaster IS NOT NULL AND salaryMaster != '' ORDER BY salaryMaster`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.salaryMaster));
        }
      });
    });
  }

  // Bulk upsert for Excel imports
  bulkUpsert(dataArray) {
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      return { imported: 0, updated: 0 };
    }

    const transaction = this.db.transaction((items) => {
      let imported = 0;
      let updated = 0;

      const selectStmt = this.db.prepare(`
        SELECT id FROM ${this.tableName}
        WHERE name = ? AND payrollMonth = ?
      `);

      const insertStmt = this.db.prepare(`
        INSERT INTO ${this.tableName} (payrollMonth, name, gross, pf, salaryMaster, createdAt, updatedAt, source)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const updateStmt = this.db.prepare(`
        UPDATE ${this.tableName}
        SET gross = ?, pf = ?, salaryMaster = ?, updatedAt = ?, source = ?
        WHERE id = ?
      `);

      for (const item of items) {
        const existing = selectStmt.get(item.name, item.payrollMonth);
        const timestamp = new Date().toISOString();

        if (existing) {
          updateStmt.run(
            item.gross,
            item.pf || 0,
            item.salaryMaster || null,
            timestamp,
            item.source || 'import-update',
            existing.id
          );
          updated++;
        } else {
          insertStmt.run(
            item.payrollMonth,
            item.name,
            item.gross,
            item.pf || 0,
            item.salaryMaster || null,
            timestamp,
            timestamp,
            item.source || 'import-new'
          );
          imported++;
        }
      }

      return { imported, updated };
    });

    return transaction(dataArray);
  }

  // Get employees for discrepancy analysis
  getEmployeesByMonth(payrollMonth) {
    const query = `SELECT DISTINCT name FROM ${this.tableName} WHERE payrollMonth = ?`;
    return this.db.prepare(query).all(payrollMonth).map(row => row.name);
  }

  // Get salary summary by month
  getSalaryMonthlySummary() {
    const query = `
      SELECT
        payrollMonth,
        COUNT(DISTINCT name) as employeeCount,
        SUM(gross) as totalGross,
        SUM(pf) as totalPf,
        AVG(gross) as avgGross,
        AVG(pf) as avgPf,
        MIN(gross) as minGross,
        MAX(gross) as maxGross
      FROM ${this.tableName}
      GROUP BY payrollMonth
      ORDER BY payrollMonth
    `;
    return this.db.prepare(query).all();
  }

  // Get salary trends for an employee
  getEmployeeSalaryTrend(employeeName) {
    const query = `
      SELECT
        payrollMonth,
        gross,
        pf,
        (gross + pf) as total,
        salaryMaster
      FROM ${this.tableName}
      WHERE name = ?
      ORDER BY payrollMonth
    `;
    return this.db.prepare(query).all(employeeName);
  }

  // Get top earners for a month
  getTopEarners(payrollMonth, limit = 10) {
    const query = `
      SELECT
        name,
        gross,
        pf,
        (gross + pf) as total,
        salaryMaster
      FROM ${this.tableName}
      WHERE payrollMonth = ?
      ORDER BY gross DESC
      LIMIT ?
    `;
    return this.db.prepare(query).all(payrollMonth, limit);
  }
}

module.exports = new SalaryModel();
