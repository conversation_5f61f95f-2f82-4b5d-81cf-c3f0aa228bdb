require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');

// Import middleware
const { errorHandler, notFound } = require('./middleware/errorHandler');

// Import routes
const costCenterRoutes = require('./routes/costCenter');
const salaryRoutes = require('./routes/salary');
const resourceUtilizationRoutes = require('./routes/resourceUtilization');
const resourceCostRoutes = require('./routes/resourceCost');
const employeeDiscrepancyRoutes = require('./routes/employeeDiscrepancy');
const paginationStateRoutes = require('./routes/paginationState');

const app = express();
const PORT = process.env.PORT || 5000;

// Initialize database
const dbManager = require('./config/database');

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Financial Report Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
app.use('/api/cost-center', costCenterRoutes);
app.use('/api/salary', salaryRoutes);
app.use('/api/resource-utilization', resourceUtilizationRoutes);
app.use('/api/resource-cost', resourceCostRoutes);
app.use('/api/employee-discrepancy', employeeDiscrepancyRoutes);
app.use('/api/pagination-state', paginationStateRoutes);

// Database management endpoints
app.get('/api/database/backup', (req, res) => {
  try {
    const backupPath = dbManager.backup();
    res.json({
      success: true,
      message: 'Database backup created successfully',
      backupPath
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database backup failed',
      error: error.message
    });
  }
});

app.get('/api/database/status', (req, res) => {
  try {
    const db = dbManager.getDatabase();

    db.all(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `, [], (err, tables) => {
      if (err) {
        return res.status(500).json({
          success: false,
          message: 'Failed to get database status',
          error: err.message
        });
      }

      const tableStats = {};
      let completed = 0;

      if (tables.length === 0) {
        return res.json({
          success: true,
          data: {
            tables: [],
            recordCounts: {},
            databasePath: process.env.DB_PATH || './database/finreport.db'
          }
        });
      }

      tables.forEach(table => {
        db.get(`SELECT COUNT(*) as count FROM ${table.name}`, [], (countErr, result) => {
          if (countErr) {
            tableStats[table.name] = 'Error';
          } else {
            tableStats[table.name] = result.count;
          }

          completed++;
          if (completed === tables.length) {
            res.json({
              success: true,
              data: {
                tables: tables.map(t => t.name),
                recordCounts: tableStats,
                databasePath: process.env.DB_PATH || './database/finreport.db'
              }
            });
          }
        });
      });
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get database status',
      error: error.message
    });
  }
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../build')));

  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../build/index.html'));
  });
}

// Error handling middleware (must be last)
app.use(notFound);
app.use(errorHandler);

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT. Graceful shutdown...');
  dbManager.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nReceived SIGTERM. Graceful shutdown...');
  dbManager.close();
  process.exit(0);
});

// Start server with database initialization
async function startServer() {
  try {
    // Initialize database first
    await dbManager.init();
    console.log('✅ Database initialized successfully');

    // Start the server
    app.listen(PORT, () => {
      console.log(`
🚀 Financial Report Server is running!
📍 Port: ${PORT}
🌍 Environment: ${process.env.NODE_ENV || 'development'}
🗄️  Database: ${process.env.DB_PATH || './database/finreport.db'}
🔗 Health Check: http://localhost:${PORT}/health
      `);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
