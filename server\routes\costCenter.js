const express = require('express');
const router = express.Router();
const CostCenterModel = require('../models/CostCenterModel');
const { validate, validateBulk, schemas } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

// GET /api/cost-center - Get all cost center records with filtering and pagination
router.get('/', validate(schemas.queryParams, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, month, employee, costCenter, search } = req.query;

  const filters = {};
  if (month) filters.month = month;
  if (employee) filters.name = employee;
  if (costCenter) filters.costCenter = costCenter;
  if (search) filters.search = search;

  const offset = (page - 1) * limit;
  const pagination = { limit, offset };

  const data = await CostCenterModel.findAll(filters, pagination);
  const total = await CostCenterModel.count(filters);

  res.json({
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
}));

// GET /api/cost-center/:id - Get single cost center record
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const data = await CostCenterModel.findById(id);

  if (!data) {
    return res.status(404).json({
      success: false,
      message: 'Cost center record not found'
    });
  }

  res.json({
    success: true,
    data
  });
}));

// POST /api/cost-center - Create new cost center record
router.post('/', validate(schemas.costCenter), asyncHandler(async (req, res) => {
  const data = await CostCenterModel.create(req.body);

  res.status(201).json({
    success: true,
    data,
    message: 'Cost center record created successfully'
  });
}));

// POST /api/cost-center/bulk - Bulk create/update cost center records
router.post('/bulk', validateBulk(schemas.costCenter), asyncHandler(async (req, res) => {
  const result = await CostCenterModel.bulkUpsert(req.body);

  res.json({
    success: true,
    data: result,
    message: `Bulk operation completed: ${result.imported} imported, ${result.updated} updated`
  });
}));

// PUT /api/cost-center/:id - Update cost center record
router.put('/:id', validate(schemas.costCenter), asyncHandler(async (req, res) => {
  const { id } = req.params;
  const data = await CostCenterModel.update(id, req.body);

  res.json({
    success: true,
    data,
    message: 'Cost center record updated successfully'
  });
}));

// DELETE /api/cost-center/:id - Delete single cost center record
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const result = await CostCenterModel.delete(id);

  res.json({
    success: true,
    data: result,
    message: 'Cost center record deleted successfully'
  });
}));

// DELETE /api/cost-center - Delete multiple cost center records
router.delete('/', asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'IDs array is required'
    });
  }

  const result = await CostCenterModel.deleteMany(ids);

  res.json({
    success: true,
    data: result,
    message: `${result.deletedCount} cost center records deleted successfully`
  });
}));

// DELETE /api/cost-center/clear - Clear all cost center data
router.delete('/clear', asyncHandler(async (req, res) => {
  const result = await CostCenterModel.clearAll();

  res.json({
    success: true,
    data: result,
    message: 'All cost center data cleared successfully'
  });
}));

// GET /api/cost-center/filters/months - Get unique months
router.get('/filters/months', asyncHandler(async (req, res) => {
  const months = await CostCenterModel.getUniqueMonths();

  res.json({
    success: true,
    data: months
  });
}));

// GET /api/cost-center/filters/employees - Get unique employees
router.get('/filters/employees', asyncHandler(async (req, res) => {
  const employees = await CostCenterModel.getUniqueEmployees();

  res.json({
    success: true,
    data: employees
  });
}));

// GET /api/cost-center/filters/cost-centers - Get unique cost centers
router.get('/filters/cost-centers', asyncHandler(async (req, res) => {
  const costCenters = await CostCenterModel.getUniqueCostCenters();

  res.json({
    success: true,
    data: costCenters
  });
}));

// GET /api/cost-center/summary/monthly - Get monthly summary
router.get('/summary/monthly', asyncHandler(async (req, res) => {
  const summary = await CostCenterModel.getMonthlySummary();

  res.json({
    success: true,
    data: summary
  });
}));

module.exports = router;
